const FavoriteController = require('../controllers/favorites.controller');
const { authHook } = require('../../../hooks/auth.hook');

async function favoritesRoutes(fastify, options) {
  const favoriteController = new FavoriteController();

  // Add listing to favorites
  fastify.post('/listings/:id/favorite', {
    preHandlers: [authHook],
    schema: {
      tags: ['favorites'],
      summary: 'Add listing to favorites',
      description: 'Add a specific listing to user\'s favorites',
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string', format: 'uuid' }
        },
        required: ['id']
      },
      response: {
        201: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' },
            data: { type: 'object' }
          }
        }
      }
    },
    handler: favoriteController.addToFavorites.bind(favoriteController)
  });

  // Remove listing from favorites
  fastify.delete('/listings/:id/favorite', {
    preHandlers: [authHook],
    schema: {
      tags: ['favorites'],
      summary: 'Remove listing from favorites',
      description: 'Remove a specific listing from user\'s favorites',
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string', format: 'uuid' }
        },
        required: ['id']
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' }
          }
        }
      }
    },
    handler: favoriteController.removeFromFavorites.bind(favoriteController)
  });

  // Get user's favorites
  fastify.get('/my-favorites', {
    preHandlers: [authHook],
    schema: {
      tags: ['favorites'],
      summary: 'Get user favorites',
      description: 'Get all listings favorited by the authenticated user with full listing details',
      security: [{ bearerAuth: [] }],
      querystring: {
        type: 'object',
        properties: {
          page: { type: 'integer', minimum: 1, default: 1 },
          limit: { type: 'integer', minimum: 1, maximum: 100, default: 20 }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'string', format: 'uuid' },
                  listingId: { type: 'string', format: 'uuid' },
                  userId: { type: 'string', format: 'uuid' },
                  createdAt: { type: 'string', format: 'date-time' },
                  updatedAt: { type: 'string', format: 'date-time' },
                  listing: {
                    type: 'object',
                    properties: {
                      id: { type: 'string', format: 'uuid' },
                      title: { type: 'string' },
                      description: { type: 'string' },
                      price: { type: 'number' },
                      location: { type: 'string' },
                      category: { type: 'string' },
                      subcategory: { type: 'string' },
                      images: { type: 'array', items: { type: 'string' } },
                      status: { type: 'string' },
                      createdAt: { type: 'string', format: 'date-time' },
                      updatedAt: { type: 'string', format: 'date-time' },
                      owner: {
                        type: 'object',
                        properties: {
                          id: { type: 'string', format: 'uuid' },
                          firstName: { type: 'string' },
                          lastName: { type: 'string' },
                          email: { type: 'string' },
                          phoneNumber: { type: 'string' }
                        }
                      }
                    }
                  }
                }
              }
            },
            pagination: {
              type: 'object',
              properties: {
                page: { type: 'integer' },
                limit: { type: 'integer' },
                total: { type: 'integer' },
                totalPages: { type: 'integer' }
              }
            }
          }
        }
      }
    },
    handler: favoriteController.getUserFavorites.bind(favoriteController)
  });

  // Check if listing is favorited
  fastify.get('/listings/:id/favorite-status', {
    preHandlers: [authHook],
    schema: {
      tags: ['favorites'],
      summary: 'Check favorite status',
      description: 'Check if a specific listing is favorited by the authenticated user',
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string', format: 'uuid' }
        },
        required: ['id']
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                isFavorited: { type: 'boolean' },
                favoriteId: { type: 'string', nullable: true }
              }
            }
          }
        }
      }
    },
    handler: favoriteController.checkFavoriteStatus.bind(favoriteController)
  });
}

module.exports = favoritesRoutes;
