const { getDatabase } = require('../../../config/database.config');
const { logger } = require('../../../utils/logger');

class FavoriteController {
  constructor() {
    this.db = getDatabase('write');
  }

  async addToFavorites(request, reply) {
    try {
      const { id } = request.params;
      const userId = request.user.id;

      const favorite = await this.db.models.Favorite.create({
        UserID: userId,
        ListingID: id
      });

      return reply.code(201).send({
        success: true,
        message: 'Listing added to favorites',
        data: favorite
      });
    } catch (error) {
      logger.error('Error adding to favorites:', error);
      return reply.code(500).send({
        success: false,
        message: 'Error adding to favorites',
        error: error.message
      });
    }
  }

  async removeFromFavorites(request, reply) {
    try {
      const { id } = request.params;
      const userId = request.user.id;

      await this.db.models.Favorite.destroy({
        where: {
          UserID: userId,
          ListingID: id
        }
      });

      return reply.code(200).send({
        success: true,
        message: 'Listing removed from favorites'
      });
    } catch (error) {
      logger.error('Error removing from favorites:', error);
      return reply.code(500).send({
        success: false,
        message: 'Error removing from favorites',
        error: error.message
      });
    }
  }

  async getUserFavorites(request, reply) {
    try {
      const userId = request.user.id;
      const { page = 1, limit = 20 } = request.query;

      // Get total count for pagination
      const totalCount = await this.db.models.Favorite.count({
        where: { UserID: userId }
      });

      const favorites = await this.db.models.Favorite.findAll({
        where: { UserID: userId },
        include: [{
          model: this.db.models.Listing,
          as: 'Listing',
          include: [
            {
              model: this.db.models.User,
              as: 'Lister',
              attributes: ['ID', 'FirstName', 'LastName', 'Email', 'PhoneNumber']
            }
          ]
        }],
        limit: parseInt(limit),
        offset: (page - 1) * parseInt(limit),
        order: [['CreatedAt', 'DESC']]
      });

      // Transform the data to include listing details directly
      const transformedFavorites = favorites.map(favorite => {
        const favoriteData = favorite.toJSON();
        return {
          id: favoriteData.ID,
          listingId: favoriteData.ListingID,
          userId: favoriteData.UserID,
          createdAt: favoriteData.CreatedAt,
          updatedAt: favoriteData.UpdatedAt,
          listing: favoriteData.Listing ? {
            id: favoriteData.Listing.ID,
            title: favoriteData.Listing.Title,
            description: favoriteData.Listing.Description,
            price: favoriteData.Listing.Price,
            location: favoriteData.Listing.Location,
            category: favoriteData.Listing.Category,
            subcategory: favoriteData.Listing.Subcategory,
            images: favoriteData.Listing.Images || [],
            status: favoriteData.Listing.Status,
            createdAt: favoriteData.Listing.CreatedAt,
            updatedAt: favoriteData.Listing.UpdatedAt,
            owner: favoriteData.Listing.Lister ? {
              id: favoriteData.Listing.Lister.id,
              firstName: favoriteData.Listing.Lister.fullName,
              email: favoriteData.Listing.Lister.email,
              phoneNumber: favoriteData.Listing.Lister.phone
            } : null
          } : null
        };
      });

      return reply.code(200).send({
        success: true,
        data: transformedFavorites,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: totalCount,
          totalPages: Math.ceil(totalCount / parseInt(limit))
        }
      });
    } catch (error) {
      logger.error('Error getting user favorites:', error);
      return reply.code(500).send({
        success: false,
        message: 'Error getting user favorites',
        error: error.message
      });
    }
  }

  async checkFavoriteStatus(request, reply) {
    try {
      const { id } = request.params;
      const userId = request.user.id;

      const favorite = await this.db.models.Favorite.findOne({
        where: {
          UserID: userId,
          ListingID: id
        }
      });

      return reply.code(200).send({
        success: true,
        data: {
          isFavorited: !!favorite,
          favoriteId: favorite ? favorite.ID : null
        }
      });
    } catch (error) {
      logger.error('Error checking favorite status:', error);
      return reply.code(500).send({
        success: false,
        message: 'Error checking favorite status',
        error: error.message
      });
    }
  }
}

module.exports = FavoriteController;
