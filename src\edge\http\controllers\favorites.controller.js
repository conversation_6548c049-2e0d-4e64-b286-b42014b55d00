const { getDatabase } = require('../../../config/database.config');
const { logger } = require('../../../utils/logger');

class FavoriteController {
  constructor() {
    this.db = getDatabase('write');
  }

  async addToFavorites(request, reply) {
    try {
      const { id } = request.params;
      const userId = request.user.id;

      const favorite = await this.db.models.Favorite.create({
        UserID: userId,
        ListingID: id
      });

      return reply.code(201).send({
        success: true,
        message: 'Listing added to favorites',
        data: favorite
      });
    } catch (error) {
      logger.error('Error adding to favorites:', error);
      return reply.code(500).send({
        success: false,
        message: 'Error adding to favorites',
        error: error.message
      });
    }
  }

  async removeFromFavorites(request, reply) {
    try {
      const { id } = request.params;
      const userId = request.user.id;

      await this.db.models.Favorite.destroy({
        where: {
          UserID: userId,
          ListingID: id
        }
      });

      return reply.code(200).send({
        success: true,
        message: 'Listing removed from favorites'
      });
    } catch (error) {
      logger.error('Error removing from favorites:', error);
      return reply.code(500).send({
        success: false,
        message: 'Error removing from favorites',
        error: error.message
      });
    }
  }

  async getUserFavorites(request, reply) {
    try {
      const userId = request.user.id;
      const { page = 1, limit = 20 } = request.query;

      const favorites = await this.db.models.Favorite.findAll({
        where: { UserID: userId },
        include: [{
          model: this.db.models.Listing,
          as: 'Listing'
        }],
        limit: parseInt(limit),
        offset: (page - 1) * parseInt(limit),
        order: [['CreatedAt', 'DESC']]
      });

      return reply.code(200).send({
        success: true,
        data: favorites
      });
    } catch (error) {
      logger.error('Error getting user favorites:', error);
      return reply.code(500).send({
        success: false,
        message: 'Error getting user favorites',
        error: error.message
      });
    }
  }

  async checkFavoriteStatus(request, reply) {
    try {
      const { id } = request.params;
      const userId = request.user.id;

      const favorite = await this.db.models.Favorite.findOne({
        where: {
          UserID: userId,
          ListingID: id
        }
      });

      return reply.code(200).send({
        success: true,
        data: {
          isFavorited: !!favorite,
          favoriteId: favorite ? favorite.ID : null
        }
      });
    } catch (error) {
      logger.error('Error checking favorite status:', error);
      return reply.code(500).send({
        success: false,
        message: 'Error checking favorite status',
        error: error.message
      });
    }
  }
}

module.exports = FavoriteController;
